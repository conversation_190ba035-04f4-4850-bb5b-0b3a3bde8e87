/**
 * Simple test script for PDF utilities
 * Run with: npx tsx test-pdf-utils.ts
 */

import { verifyPDFPageCountFromUrl } from '@/lib/pdf-utils';

async function testPDFUtils() {
  console.log('Testing PDF utilities...\n');

  // Test with a sample PDF URL (you can replace this with any publicly accessible PDF)
  const testPdfUrl =
    'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

  try {
    console.log(`Testing PDF from URL: ${testPdfUrl}`);
    const pdfInfo = await verifyPDFPageCountFromUrl(testPdfUrl);

    console.log('✅ PDF verification successful!');
    console.log('PDF Info:', {
      pageCount: pdfInfo.pageCount,
      title: pdfInfo.title || 'N/A',
      author: pdfInfo.author || 'N/A',
      subject: pdfInfo.subject || 'N/A',
      creator: pdfInfo.creator || 'N/A',
      producer: pdfInfo.producer || 'N/A',
      creationDate: pdfInfo.creationDate?.toISOString() || 'N/A',
      modificationDate: pdfInfo.modificationDate?.toISOString() || 'N/A',
    });
  } catch (error) {
    console.error('❌ PDF verification failed:', error);

    // Try with a different test PDF
    console.log('\nTrying with a different test PDF...');
    try {
      // This is a simple single-page PDF from Mozilla
      const altTestUrl =
        'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf';
      console.log(`Testing PDF from URL: ${altTestUrl}`);

      const altPdfInfo = await verifyPDFPageCountFromUrl(altTestUrl);
      console.log('✅ Alternative PDF verification successful!');
      console.log('PDF Info:', {
        pageCount: altPdfInfo.pageCount,
        title: altPdfInfo.title || 'N/A',
        author: altPdfInfo.author || 'N/A',
      });
    } catch (altError) {
      console.error('❌ Alternative PDF verification also failed:', altError);
      console.log(
        '\n⚠️  This might be due to network issues or PDF.js configuration in Node.js environment.'
      );
      console.log(
        'The PDF verification should work properly in the actual Next.js application context.'
      );
    }
  }
}

// Run the test
testPDFUtils().catch(console.error);
