import { prisma } from '../prisma';
import type { DocumentStatus } from './types';

// Document operations
export const documentOperations = {
  async create(data: {
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    storageUrl: string;
    userId: string;
    pageCount?: number;
  }) {
    return prisma.document.create({
      data: {
        ...data,
        pageCount: data.pageCount || 1, // Default to 1 if not provided
      },
    });
  },

  async updateStatus(documentId: string, status: DocumentStatus) {
    return prisma.document.update({
      where: { id: documentId },
      data: { status },
    });
  },

  async findByUser(userId: string, limit = 10, offset = 0) {
    return prisma.document.findMany({
      where: { userId },
      orderBy: { uploadedAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        jobs: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
    });
  },

  // Calculate credits required for document processing
  calculateCreditsRequired(pageCount: number): number {
    // Flat rate: 1 credit = 1 page
    return pageCount;
  },

  // Helper to get page count from document record
  async getPageCount(documentId: string): Promise<number> {
    const document = await prisma.document.findUnique({
      where: { id: documentId },
      select: { pageCount: true },
    });

    return document?.pageCount || 1;
  },

  async findById(documentId: string) {
    return prisma.document.findUnique({
      where: { id: documentId },
    });
  },

  async updatePageCount(
    documentId: string,
    pageCount: number,
    creditsUsed: number
  ) {
    return prisma.document.update({
      where: { id: documentId },
      data: {
        pageCount,
        creditsUsed,
      },
    });
  },
};
