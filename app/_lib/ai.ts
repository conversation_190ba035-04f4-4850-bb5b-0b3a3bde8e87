import { callMistralOCRAPI } from './providers/mistral-ocr';

export type AIResult = {
  extractedData: Record<string, unknown>;
  confidence?: number;
};
export type AIOptions = {
  documentUrl: string;
  templateSchema?: Record<string, unknown>;
};

export async function processDocumentWithAI(
  options: AIOptions,
  provider: 'mistral-ocr' | 'openai-gpt4o' = 'mistral-ocr'
): Promise<AIResult> {
  switch (provider) {
    case 'mistral-ocr':
      return callMistralOCRAPI(options);
    default:
      throw new Error('Unsupported AI provider');
  }
}
