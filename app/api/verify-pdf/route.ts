import { NextRequest, NextResponse } from 'next/server';
import { requireDbUser } from '@/lib/auth';
import { documentOperations, creditOperations } from '@/lib/db';
import { verifyPDFPageCountFromUrl } from '@/lib/pdf-utils';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const user = await requireDbUser();

    // Parse request body
    const body = await request.json();
    const { documentId } = body;

    if (!documentId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required field: documentId',
        },
        { status: 400 }
      );
    }

    // Get document from database
    const document = await documentOperations.findById(documentId);
    if (!document) {
      return NextResponse.json(
        {
          success: false,
          error: 'Document not found',
        },
        { status: 404 }
      );
    }

    // Verify user owns the document
    if (document.userId !== user.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized access to document',
        },
        { status: 403 }
      );
    }

    // Only verify PDFs
    if (document.mimeType !== 'application/pdf') {
      return NextResponse.json(
        {
          success: true,
          verified: true,
          message: 'Non-PDF files do not require verification',
          pageCount: document.pageCount,
        }
      );
    }

    try {
      // Verify PDF page count from the uploaded file
      const pdfInfo = await verifyPDFPageCountFromUrl(document.storageUrl);
      const actualPageCount = pdfInfo.pageCount;
      const storedPageCount = document.pageCount;

      // Check if page counts match
      const isValid = actualPageCount === storedPageCount;

      if (!isValid) {
        // Page count mismatch - need to adjust credits
        const creditDifference = actualPageCount - storedPageCount;

        if (creditDifference > 0) {
          // Need more credits - check if user has enough
          if (user.creditBalance < creditDifference) {
            return NextResponse.json(
              {
                success: false,
                error: `Insufficient credits. Document has ${actualPageCount} pages but only ${storedPageCount} credits were reserved. Need ${creditDifference} more credits.`,
                pageCountMismatch: true,
                actualPageCount,
                storedPageCount,
                creditDifference,
              },
              { status: 402 } // Payment Required
            );
          }

          // Consume additional credits
          await creditOperations.consumeCredits(
            user.id,
            creditDifference,
            `Additional credits for document verification: ${document.originalName} (${creditDifference} extra pages)`,
            undefined,
            document.id
          );
        } else if (creditDifference < 0) {
          // Refund excess credits
          await creditOperations.addCredits(
            user.id,
            Math.abs(creditDifference),
            'REFUND',
            `Credit refund for document verification: ${document.originalName} (${Math.abs(creditDifference)} pages overcharged)`
          );
        }

        // Update document with correct page count and credits
        await documentOperations.updatePageCount(
          document.id,
          actualPageCount,
          actualPageCount // Update credits used to match actual page count
        );
      }

      return NextResponse.json({
        success: true,
        verified: true,
        pageCountMatch: isValid,
        actualPageCount,
        storedPageCount,
        creditAdjustment: isValid ? 0 : actualPageCount - storedPageCount,
        pdfInfo: {
          title: pdfInfo.title,
          author: pdfInfo.author,
          subject: pdfInfo.subject,
          creator: pdfInfo.creator,
          producer: pdfInfo.producer,
          creationDate: pdfInfo.creationDate,
          modificationDate: pdfInfo.modificationDate,
        },
      });
    } catch (pdfError) {
      console.error('PDF verification error:', pdfError);
      return NextResponse.json(
        {
          success: false,
          error: `Failed to verify PDF: ${pdfError instanceof Error ? pdfError.message : 'Unknown error'}`,
          verified: false,
        },
        { status: 422 } // Unprocessable Entity
      );
    }
  } catch (error) {
    console.error('Error verifying PDF:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
