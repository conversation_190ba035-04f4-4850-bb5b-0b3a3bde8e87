import { NextRequest, NextResponse } from 'next/server';
import { requireDbUser } from '@/lib/auth';
import {
  generatePresignedUploadUrl,
  generateFileKey,
  validateFile,
  getFileUrl,
} from '@/lib/s3';
import { documentOperations, jobOperations } from '@/lib/db';
import { redis } from '@/lib/queue';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user and get database user
    const user = await requireDbUser();

    // Check if user has credits
    if (user.creditBalance <= 0) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Insufficient credits. Please purchase more credits to upload documents.',
        },
        { status: 402 } // Payment Required
      );
    }

    // Parse request body
    const body = await request.json();
    const { filename, contentType, fileSize } = body;

    if (!filename || !contentType || !fileSize) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: filename, contentType, fileSize',
        },
        { status: 400 }
      );
    }

    // Validate file
    try {
      validateFile({ type: contentType, size: fileSize });
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Invalid file',
        },
        { status: 400 }
      );
    }

    // Generate unique file key
    const fileKey = generateFileKey(user.id, filename);

    // Generate presigned URL
    const presignedUrl = await generatePresignedUploadUrl(
      fileKey,
      contentType,
      3600 // 1 hour expiration
    );

    // Create Document record in DB
    let pageCount = 1;

    if (contentType === 'application/pdf') {
      // Get page count from the request body if provided
      const clientPageCount = body.pageCount;
      if (typeof clientPageCount === 'number' && clientPageCount > 0) {
        pageCount = clientPageCount;
      } else {
        // Fallback: default to 1 page if pageCount is not provided
        pageCount = 1;
      }
    } else {
      // Non-PDF files are always 1 page
      pageCount = 1;
    }

    const document = await documentOperations.create({
      filename: fileKey,
      originalName: filename,
      mimeType: contentType,
      size: fileSize,
      storageUrl: getFileUrl(fileKey),
      userId: user.id,
      pageCount,
    });

    // Create Job record in DB (status: PENDING)
    const job = await jobOperations.create({
      userId: user.id,
      documentId: document.id,
      type: 'EXTRACT', // Default job type
    });

    // Publish jobId to Upstash Redis queue
    await redis.lpush('jobs-queue', job.id);

    return NextResponse.json({
      success: true,
      data: {
        presignedUrl,
        fileKey,
        uploadUrl: presignedUrl, // For compatibility
        jobId: job.id,
        documentId: document.id,
      },
    });
  } catch (error) {
    console.error('Error generating presigned URL:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
