import { NextRequest, NextResponse } from 'next/server';
import { requireDbUser } from '@/lib/auth';
import { templateOperations } from '@/lib/db/templates';

export async function GET(req: NextRequest) {
  try {
    const user = await requireDbUser();
    
    // Fetch both public templates and user's custom templates
    const [publicTemplates, userTemplates] = await Promise.all([
      templateOperations.findPublic(),
      templateOperations.findByUser(user.id),
    ]);
    
    return NextResponse.json({
      success: true,
      data: {
        public: publicTemplates,
        user: userTemplates,
      },
    });
  } catch (error) {
    console.error('Error fetching templates:', error);
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const user = await requireDbUser();
    const body = await req.json();
    
    const { name, description, documentType, fields } = body;
    
    if (!name || !documentType || !fields) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: name, documentType, fields' },
        { status: 400 }
      );
    }
    
    const template = await templateOperations.create({
      name,
      description,
      documentType,
      fields,
      userId: user.id,
    });
    
    return NextResponse.json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error('Error creating template:', error);
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
