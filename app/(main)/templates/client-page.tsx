'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { TemplateCard } from '@/components/template-card';
import { TemplateCreateDialog } from '@/components/template-create-dialog';
import { Plus, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface TemplateField {
  type: string;
  required: boolean;
  description?: string;
}

interface Template {
  id: string;
  name: string;
  description?: string;
  documentType: string;
  fields: Record<string, TemplateField>;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  userId?: string;
}

interface TemplatesData {
  public: Template[];
  user: Template[];
}

export default function TemplatesClientPage() {
  const [templates, setTemplates] = useState<TemplatesData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/templates');
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch templates');
      }

      setTemplates(result.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching templates:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const handleCreateTemplate = async (templateData: {
    name: string;
    description?: string;
    documentType: string;
    fields: Record<string, TemplateField>;
  }) => {
    try {
      setCreateLoading(true);
      const response = await fetch('/api/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to create template');
      }

      // Refresh templates list
      await fetchTemplates();
      setCreateDialogOpen(false);
    } catch (err) {
      console.error('Error creating template:', err);
      // You might want to show a toast notification here
    } finally {
      setCreateLoading(false);
    }
  };

  const filteredPublicTemplates = templates?.public.filter(template =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.documentType.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const filteredUserTemplates = templates?.user.filter(template =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.documentType.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  if (loading) {
    return (
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-64" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Error Loading Templates
            </h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={fetchTemplates}>Try Again</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Templates</h1>
            <p className="text-gray-600">
              Manage data extraction templates for your documents
            </p>
          </div>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </div>

        <div className="mb-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Public Templates Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Built-in Templates</h2>
          {filteredPublicTemplates.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPublicTemplates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  onUse={(template) => {
                    // TODO: Implement template usage
                    console.log('Using template:', template);
                  }}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {searchQuery ? 'No built-in templates match your search.' : 'No built-in templates available.'}
            </div>
          )}
        </div>

        {/* User Templates Section */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Your Templates</h2>
          {filteredUserTemplates.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredUserTemplates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  onUse={(template) => {
                    // TODO: Implement template usage
                    console.log('Using template:', template);
                  }}
                  onEdit={(template) => {
                    // TODO: Implement template editing
                    console.log('Editing template:', template);
                  }}
                  onDelete={(template) => {
                    // TODO: Implement template deletion
                    console.log('Deleting template:', template);
                  }}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {searchQuery ? 'No custom templates match your search.' : 'You haven\'t created any templates yet.'}
            </div>
          )}
        </div>

        <TemplateCreateDialog
          open={createDialogOpen}
          onOpenChange={setCreateDialogOpen}
          onSubmit={handleCreateTemplate}
          loading={createLoading}
        />
      </div>
    </div>
  );
}
