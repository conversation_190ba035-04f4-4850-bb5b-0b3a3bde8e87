import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { AppTitle } from '@/components/app-title';
import { Metadata } from 'next';
import { <PERSON> } from './hero';
import { AccountInfo } from './account-info';

export const metadata: Metadata = {
  title: 'Dashboard',
};

export default async function DashboardPage() {
  return (
    <>
      <AppTitle title="Dashboard" />
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <Hero />

          <AccountInfo />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-2">Upload Documents</h3>
              <p className="text-gray-600 mb-4">
                Start extracting data from your documents
              </p>
              <Button asChild className="w-full">
                <Link href="/documents">Go to Documents</Link>
              </Button>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-2">View Templates</h3>
              <p className="text-gray-600 mb-4">
                Manage your data extraction templates
              </p>
              <Button asChild className="w-full">
                <Link href="/templates">View Templates</Link>
              </Button>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-2">API Access</h3>
              <p className="text-gray-600 mb-4">
                Generate API keys for integration
              </p>
              <Button variant="outline" className="w-full">
                Coming Soon
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
