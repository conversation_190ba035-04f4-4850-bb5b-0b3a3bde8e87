'use client';

import { PlusCircle, type LucideIcon } from 'lucide-react';
import { usePathname } from 'next/navigation';

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import Link from 'next/link';

export type NavigationItem = {
  title: string;
  url: string;
  icon?: LucideIcon;
};
export type NavigationGroup = {
  title?: string;
  items: NavigationItem[];
};

export type NavMainProps = {
  groups: NavigationGroup[];
};

export function NavMain({ groups }: NavMainProps) {
  const pathname = usePathname();

  return (
    <>
      {groups.map((group, index) => (
        <SidebarGroup key={index}>
          {group.title && <SidebarGroupLabel>{group.title}</SidebarGroupLabel>}
          <SidebarGroupContent>
            <SidebarMenu>
              {group.items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    tooltip={item.title}
                    asChild
                    isActive={pathname === item.url}
                  >
                    <Link href={item.url}>
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      ))}
    </>
  );
}
