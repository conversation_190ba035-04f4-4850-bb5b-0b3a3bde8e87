'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Upload, X, FileText, Image } from 'lucide-react';

interface FileUploadState {
  file: File | null;
  uploading: boolean;
  progress: number;
  error: string | null;
  success: boolean;
}

export function FileUploader() {
  const [state, setState] = useState<FileUploadState>({
    file: null,
    uploading: false,
    progress: 0,
    error: null,
    success: false,
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const selectedFile = event.target.files[0];
      setState((prev) => ({
        ...prev,
        file: selectedFile,
        error: null,
        success: false,
      }));
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer.files;
    if (files && files[0]) {
      setState((prev) => ({
        ...prev,
        file: files[0],
        error: null,
        success: false,
      }));
    }

    // Remove drag styling
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.remove('border-indigo-500', 'bg-indigo-50');
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    // Add drag styling
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.add('border-indigo-500', 'bg-indigo-50');
    }
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    // Remove drag styling
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.remove('border-indigo-500', 'bg-indigo-50');
    }
  };

  const clearFile = () => {
    setState((prev) => ({
      ...prev,
      file: null,
      error: null,
      success: false,
      progress: 0,
    }));

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getPdfPageCount = async (file: File): Promise<number | null> => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      // Use PDF.js to parse the PDF and get the page count
      // Dynamically import pdfjs-dist to avoid SSR issues
      const pdfjsLib = await import('pdfjs-dist');

      // Set worker source for pdfjs-dist
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.js`;

      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      return pdf.numPages;
    } catch (e) {
      console.error('Failed to count PDF pages:', e);
      return null;
    }
  };

  const handleUpload = async () => {
    if (!state.file) return;

    setState((prev) => ({
      ...prev,
      uploading: true,
      progress: 0,
      error: null,
    }));

    try {
      // Step 1: Get presigned URL
      setState((prev) => ({ ...prev, progress: 10 }));

      let pageCount: number | undefined = undefined;
      if (state.file.type === 'application/pdf') {
        pageCount = (await getPdfPageCount(state.file)) || undefined;
      }

      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: state.file.name,
          contentType: state.file.type,
          fileSize: state.file.size,
          ...(pageCount ? { pageCount } : {}),
        }),
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.error || 'Failed to get upload URL');
      }

      const { data } = await uploadResponse.json();
      const { presignedUrl } = data;

      // Step 2: Upload file directly to S3
      setState((prev) => ({ ...prev, progress: 30 }));

      const s3Response = await fetch(presignedUrl, {
        method: 'PUT',
        body: state.file,
        headers: {
          'Content-Type': state.file.type,
        },
      });

      if (!s3Response.ok) {
        throw new Error('Failed to upload file to storage');
      }

      setState((prev) => ({ ...prev, progress: 80 }));

      // Step 3: Verify PDF page count if it's a PDF
      if (state.file.type === 'application/pdf') {
        try {
          const verifyResponse = await fetch('/api/verify-pdf', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              documentId: data.documentId,
            }),
          });

          if (!verifyResponse.ok) {
            const verifyError = await verifyResponse.json();
            console.warn('PDF verification failed:', verifyError);
            // Don't fail the upload, just log the warning
          } else {
            const verifyData = await verifyResponse.json();
            if (!verifyData.pageCountMatch) {
              console.warn('Page count mismatch detected:', verifyData);
            }
          }
        } catch (verifyError) {
          console.warn('PDF verification error:', verifyError);
          // Don't fail the upload, just log the warning
        }
      }

      setState((prev) => ({
        ...prev,
        progress: 100,
        success: true,
        uploading: false,
      }));

      // Clear file after successful upload
      setTimeout(() => {
        clearFile();
      }, 2000);
    } catch (error) {
      console.error('Upload error:', error);
      setState((prev) => ({
        ...prev,
        uploading: false,
        error: error instanceof Error ? error.message : 'Upload failed',
        progress: 0,
      }));
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return (
        <Image className="h-8 w-8 text-blue-500" aria-label="Image file" />
      );
    }
    return (
      <FileText className="h-8 w-8 text-red-500" aria-label="Document file" />
    );
  };

  return (
    <div className="space-y-4">
      {/* Drop Zone */}
      <div
        ref={dropZoneRef}
        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-indigo-500 hover:bg-indigo-50 transition-colors"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <Upload className="mx-auto h-12 w-12 text-gray-400" />
        <p className="mt-2 text-lg font-semibold text-gray-700">
          Drag & drop files here
        </p>
        <p className="text-gray-500">or click to browse</p>
        <p className="text-sm text-gray-400 mt-1">
          PDF, JPEG, PNG, GIF, WebP (max 10MB)
        </p>
      </div>

      {/* Hidden file input */}
      <Input
        ref={fileInputRef}
        type="file"
        onChange={handleFileChange}
        accept="application/pdf,image/*"
        className="hidden"
      />

      {/* Selected file display */}
      {state.file && (
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            {getFileIcon(state.file)}
            <div>
              <p className="font-medium text-sm">{state.file.name}</p>
              <p className="text-xs text-gray-500">
                {(state.file.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          </div>
          {!state.uploading && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFile}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      )}

      {/* Progress bar */}
      {state.uploading && (
        <div className="space-y-2">
          <Progress value={state.progress} className="w-full" />
          <p className="text-sm text-gray-600 text-center">
            Uploading... {state.progress}%
          </p>
        </div>
      )}

      {/* Error message */}
      {state.error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{state.error}</p>
        </div>
      )}

      {/* Success message */}
      {state.success && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-600">
            File uploaded successfully! Processing will begin shortly.
          </p>
        </div>
      )}

      {/* Upload button */}
      <Button
        onClick={handleUpload}
        disabled={!state.file || state.uploading}
        className="w-full"
      >
        {state.uploading ? 'Uploading...' : 'Upload Document'}
      </Button>
    </div>
  );
}
