'use client';

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FileText, Calendar, User, Globe } from 'lucide-react';

interface TemplateField {
  type: string;
  required: boolean;
  description?: string;
}

interface Template {
  id: string;
  name: string;
  description?: string;
  documentType: string;
  fields: Record<string, TemplateField>;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  userId?: string;
}

interface TemplateCardProps {
  template: Template;
  onUse?: (template: Template) => void;
  onEdit?: (template: Template) => void;
  onDelete?: (template: Template) => void;
}

export function TemplateCard({
  template,
  onUse,
  onEdit,
  onDelete,
}: TemplateCardProps) {
  const fieldCount = Object.keys(template.fields).length;
  const requiredFieldCount = Object.values(template.fields).filter(
    (field) => field.required
  ).length;

  const getDocumentTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'invoice':
        return 'bg-blue-100 text-blue-800';
      case 'receipt':
        return 'bg-green-100 text-green-800';
      case 'business_card':
        return 'bg-purple-100 text-purple-800';
      case 'form':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="h-full hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold truncate">
              {template.name}
            </CardTitle>
            <CardDescription
              className="mt-1 text-sm text-gray-600 overflow-hidden"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
              }}
            >
              {template.description || 'No description provided'}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2 ml-2">
            {template.isPublic ? (
              <Globe
                className="h-4 w-4 text-blue-600"
                title="Public template"
              />
            ) : (
              <User className="h-4 w-4 text-gray-600" title="Custom template" />
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Badge
            variant="secondary"
            className={getDocumentTypeColor(template.documentType)}
          >
            <FileText className="h-3 w-3 mr-1" />
            {template.documentType.replace('_', ' ')}
          </Badge>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Fields:</span>
            <span className="font-medium">{fieldCount} total</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Required:</span>
            <span className="font-medium text-red-600">
              {requiredFieldCount}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-1 text-xs text-gray-500">
          <Calendar className="h-3 w-3" />
          <span>
            {template.isPublic
              ? 'Built-in template'
              : `Created ${new Date(template.createdAt).toLocaleDateString()}`}
          </span>
        </div>

        <div className="flex gap-2 pt-2">
          {onUse && (
            <Button
              size="sm"
              className="flex-1"
              onClick={() => onUse(template)}
            >
              Use Template
            </Button>
          )}
          {!template.isPublic && onEdit && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onEdit(template)}
            >
              Edit
            </Button>
          )}
          {!template.isPublic && onDelete && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onDelete(template)}
            >
              Delete
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
