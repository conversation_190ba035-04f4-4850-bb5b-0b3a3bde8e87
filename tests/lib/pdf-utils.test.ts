import { describe, it, expect } from 'vitest';
import { verifyPDFPageCountFromUrl } from '@/lib/pdf-utils';

describe('PDF Utilities - verifyPDFPageCountFromUrl', () => {
  // Increased timeout for network requests
  const TEST_TIMEOUT = 30000; // 30 seconds

  it(
    'should correctly verify PDF info and page count from a W3C dummy PDF URL',
    async () => {
      const testPdfUrl =
        'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

      console.log(`Testing PDF from URL: ${testPdfUrl}`);
      const pdfInfo = await verifyPDFPageCountFromUrl(testPdfUrl);

      expect(pdfInfo).toBeDefined();
      expect(pdfInfo.pageCount).toBe(1);
      // Add more specific assertions if known and stable
      // For example, if the title is always "Dummy PDF document"
      // expect(pdfInfo.title).toBe('Dummy PDF document');
      console.log('W3C Dummy PDF Info:', {
        pageCount: pdfInfo.pageCount,
        title: pdfInfo.title || 'N/A',
        author: pdfInfo.author || 'N/A',
      });
    },
    TEST_TIMEOUT
  );

  it(
    'should correctly verify PDF info and page count from a Mozilla Tracemonkey PDF URL',
    async () => {
      const altTestUrl =
        'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf';

      console.log(`Testing PDF from URL: ${altTestUrl}`);
      const altPdfInfo = await verifyPDFPageCountFromUrl(altTestUrl);

      expect(altPdfInfo).toBeDefined();
      expect(altPdfInfo.pageCount).toBe(14);
      // Add more specific assertions if known and stable
      // expect(altPdfInfo.title).toBe('Trace-based Just-in-Time Type Specialization for Dynamic Languages');
      console.log('Mozilla Tracemonkey PDF Info:', {
        pageCount: altPdfInfo.pageCount,
        title: altPdfInfo.title || 'N/A',
        author: altPdfInfo.author || 'N/A',
      });
    },
    TEST_TIMEOUT
  );

  it(
    'should throw an error for an invalid PDF URL',
    async () => {
      const invalidPdfUrl = 'https://example.com/non-existent-document.pdf';
      console.log(`Testing with invalid PDF URL: ${invalidPdfUrl}`);
      await expect(verifyPDFPageCountFromUrl(invalidPdfUrl)).rejects.toThrow();
    },
    TEST_TIMEOUT
  );

  it(
    'should throw an error for a URL that does not point to a PDF',
    async () => {
      const notAPdfUrl = 'https://example.com/some-text-file.txt'; // Assuming this URL returns non-PDF content or 404s
      console.log(`Testing with non-PDF URL: ${notAPdfUrl}`);
      // This test depends on the server responding with an error or non-PDF content type
      // pdf.js might throw different errors based on content type or if it's HTML
      await expect(verifyPDFPageCountFromUrl(notAPdfUrl)).rejects.toThrow();
    },
    TEST_TIMEOUT
  );
});
