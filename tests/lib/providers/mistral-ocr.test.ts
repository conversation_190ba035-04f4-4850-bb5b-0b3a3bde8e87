import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { callMistralOCRAPI } from '@/lib/providers/mistral-ocr';
import { Mistral } from '@mistralai/mistralai';
import type { AIOptions } from '@/lib/ai';

// Mock the Mistral AI client
vi.mock('@mistralai/mistralai');

const mockOcrProcess = vi.fn();

describe('callMistralOCRAPI', () => {
  let originalApiKey: string | undefined;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Mock the Mistral class and its methods
    vi.mocked(Mistral).mockImplementation(() => {
      return {
        ocr: {
          process: mockOcrProcess,
        },
      } as unknown as Mistral;
    });

    // Backup and restore environment variables
    originalApiKey = process.env.MISTRAL_API_KEY;
    process.env.MISTRAL_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    process.env.MISTRAL_API_KEY = originalApiKey;
  });

  const defaultOptions: AIOptions = {
    documentUrl: 'https://example.com/document.pdf',
    templateSchema: {
      type: 'object',
      properties: { name: { type: 'string' } },
    },
  };

  it('should throw an error if MISTRAL_API_KEY is not set', async () => {
    delete process.env.MISTRAL_API_KEY;
    await expect(callMistralOCRAPI(defaultOptions)).rejects.toThrow(
      'MISTRAL_API_KEY is not set in environment variables'
    );
  });

  it('should throw an error if templateSchema is not provided', async () => {
    const optionsWithoutSchema = {
      ...defaultOptions,
      templateSchema: undefined,
    };
    await expect(callMistralOCRAPI(optionsWithoutSchema)).rejects.toThrow(
      '`templateSchema` is required for now'
    );
  });

  it('should call the Mistral OCR API and return structured data on success', async () => {
    const mockOcrResult = {
      documentAnnotation: JSON.stringify({ name: 'John Doe' }),
    };
    mockOcrProcess.mockResolvedValue(mockOcrResult);

    const result = await callMistralOCRAPI(defaultOptions);

    expect(mockOcrProcess).toHaveBeenCalledWith({
      model: 'mistral-ocr-latest',
      pages: [0, 1, 2, 3, 4, 5, 6, 7],
      document: {
        type: 'document_url',
        documentUrl: defaultOptions.documentUrl,
      },
      documentAnnotationFormat: {
        type: 'json_schema',
        jsonSchema: {
          name: 'document_annotation',
          schemaDefinition: defaultOptions.templateSchema,
          strict: true,
        },
      },
    });

    expect(result).toEqual({
      extractedData: { name: 'John Doe' },
      confidence: 1,
    });
  });

  it('should throw an error if the Mistral OCR API call fails', async () => {
    const errorMessage = 'API request failed';
    mockOcrProcess.mockRejectedValue(new Error(errorMessage));

    await expect(callMistralOCRAPI(defaultOptions)).rejects.toThrow(
      `Mistral OCR processing failed: ${errorMessage}`
    );
  });
});
